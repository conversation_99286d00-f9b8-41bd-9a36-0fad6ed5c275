import { eq, and, asc } from 'drizzle-orm'
import { db } from '../db/index.js'
import { vendorEmails, type VendorEmail, type NewVendorEmail } from '../db/schema.js'

export type { VendorEmail, NewVendorEmail }

export async function getVendorEmails(shop: string): Promise<VendorEmail[]> {
  return db.select().from(vendorEmails).where(eq(vendorEmails.shop, shop)).orderBy(asc(vendorEmails.vendor))
}

export async function getVendorEmail(id: string): Promise<VendorEmail | undefined> {
  const result = await db.select().from(vendorEmails).where(eq(vendorEmails.id, id)).limit(1)

  return result[0]
}

export async function getVendorEmailByVendor(shop: string, vendor: string): Promise<VendorEmail | null> {
  const result = await db
    .select()
    .from(vendorEmails)
    .where(and(eq(vendorEmails.shop, shop), eq(vendorEmails.vendor, vendor)))
    .limit(1)

  return result[0] || null
}

async function createVendorEmail(shop: string, vendor: string, email: string, ccEmail?: string): Promise<VendorEmail> {
  const newVendorEmail: NewVendorEmail = {
    shop,
    vendor,
    email,
    ccEmail,
  }

  const result = await db.insert(vendorEmails).values(newVendorEmail).returning()

  return result[0]
}

async function updateVendorEmail(id: string, vendor: string, email: string, ccEmail?: string): Promise<VendorEmail> {
  const result = await db
    .update(vendorEmails)
    .set({
      vendor,
      email,
      ccEmail,
      updatedAt: new Date(),
    })
    .where(eq(vendorEmails.id, id))
    .returning()

  if (result.length === 0) {
    throw new Error(`Vendor email with ID ${id} not found.`)
  }

  return result[0]
}

export async function upsertVendorEmail(
  shop: string,
  vendor: string,
  email: string,
  ccEmail?: string,
  id?: string,
): Promise<VendorEmail> {
  if (id) {
    return updateVendorEmail(id, vendor, email, ccEmail)
  } else {
    return createVendorEmail(shop, vendor, email, ccEmail)
  }
}

export async function updateVendorEmailById(id: string, data: { vendor: string; email: string }): Promise<VendorEmail> {
  try {
    const result = await db
      .update(vendorEmails)
      .set({
        vendor: data.vendor,
        email: data.email,
        updatedAt: new Date(),
      })
      .where(eq(vendorEmails.id, id))
      .returning()

    if (result.length === 0) {
      throw new Error(`Vendor email with ID ${id} not found.`)
    }

    return result[0]
  } catch (error) {
    console.error(`Failed to update vendor email with id ${id}:`, error)
    throw new Error(`Could not update vendor email with ID ${id}.`)
  }
}

export async function deleteVendorEmail(id: string): Promise<VendorEmail> {
  const result = await db.delete(vendorEmails).where(eq(vendorEmails.id, id)).returning()

  if (result.length === 0) {
    throw new Error(`Vendor email with ID ${id} not found.`)
  }

  return result[0]
}
